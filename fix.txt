PS C:\workspace\dev\cdpq\Dev.Local> .\launch.ps1 -c dk
Connexion Docker à AWS ECR...
Login Succeeded
Connexion Docker ECR reussie!
PS C:\workspace\dev\cdpq\Dev.Local> 
PS C:\workspace\dev\cdpq\Dev.Local> cd "C:\workspace\dev\cdpq\Dev.Local"
PS C:\workspace\dev\cdpq\Dev.Local> .\launch.ps1 -c id
Identite AWS actuelle:
Account: '************'
Arn: arn:aws:sts::************:assumed-role/AWSReservedSSO_CDPQ-PowerUser_fb459c456255730e/<EMAIL>
UserId: AROATOMARQKHOTBDLLKVB:<EMAIL>

PS C:\workspace\dev\cdpq\Dev.Local> 
PS C:\workspace\dev\cdpq\Dev.Local> cd "C:\workspace\dev\cdpq\Dev.Local"
PS C:\workspace\dev\cdpq\Dev.Local> docker pull ************.dkr.ecr.ca-central-1.amazonaws.com/cgpt-ui:latest
latest: Pulling from cgpt-ui
Digest: sha256:eb4b1bad9d7e43a2862c062d9adafe2987706551a348404d369f08fe8431e690
Status: Downloaded newer image for ************.dkr.ecr.ca-central-1.amazonaws.com/cgpt-ui:latest
************.dkr.ecr.ca-central-1.amazonaws.com/cgpt-ui:latest
PS C:\workspace\dev\cdpq\Dev.Local> 
PS C:\workspace\dev\cdpq\Dev.Local> cd "C:\workspace\dev\cdpq\Dev.Local"
PS C:\workspace\dev\cdpq\Dev.Local> .\launch.ps1
Connexion à AWS ECR avec le profil ESG-DV-PowerUser-SSO...
Deja connecte!
Aucune image JFrog detectee dans la configuration, docker login ignore.
[+] Pulling 45/55
 ✔ emp Pulled                                                                       17.1s 
 ✔ traefik Pulled                                                                    9.6s 
 ✔ bnqinvt Pulled                                                                   17.8s 
 - talperftraitement [⣤⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣦⣿⣿⣿⣿] 459.1MB / 640MB   Pulling             35.9s 
 ✔ ui Pulled                                                                        19.7s 
 - traduc [⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣄⣿⣶⣿⣄⣿⣿⣿⣿] 321.4MB / 573.4MB Pulling                         35.9s 
 - andoc [⣿⣿⣄⣿⣿] 108.1MB / 252.1MB Pulling                                          35.9s 






