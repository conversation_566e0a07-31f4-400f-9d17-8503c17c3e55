<#
.SYNOPSIS
    Script de gestion des services et configurations avec Docker Compose et Traefik.

.DESCRIPTION
    Ce script permet de gerer divers services et configurations avec de multiples parametres.
    Il prend en charge le demarrage/arret de services, la selection de branches Git,
    et l'execution de commandes AWS/Docker.

    Le script utilise les variables definies dans variables.txt pour configurer dynamiquement
    l'origine des images Docker et d'autres parametres systeme.

.PARAMETER r
    Le nom du service specifique a redemarrer. Si specifie, seul ce service sera affecte
    par les operations start/stop.

    Exemple : "andoc", "bnqinvt", "emp", "traduc", "talperftraitement", "ui"

.PARAMETER b
    La branche Git a utiliser pour le deploiement. Permet de deployer une version
    specifique du code en ajoutant "-SNAPSHOT" au nom de la branche.

    Exemples :
    - "main" : Branche principale -> "main-SNAPSHOT"
    - "develop" : Branche de developpement -> "develop-SNAPSHOT"
    - "feature/nouvelle-fonctionnalite" : Branche de fonctionnalite

.PARAMETER c
    La commande a executer. Valeurs valides :
    - "start" : Demarre les services (docker-compose up) - defaut
    - "stop" : Arrete les services (docker-compose down)
    - "help" : Affiche l'aide complete du script
    - "sso" : Connexion AWS SSO
    - "id" : Affiche l'identite AWS actuelle
    - "dk" : Connexion Docker a AWS ECR
    - "jfrog" : Connexion Docker a JFrog

.PARAMETER h
    Commutateur pour afficher l'aide detaillee du script avec Get-Help.

.NOTES
    Prerequis :
    - PowerShell 5.1 ou superieur
    - Docker et Docker Compose v2+ installes
    - AWS CLI configure (pour les commandes AWS)
    - Fichier variables.txt configure selon les besoins

    Configuration des images Docker :
    Modifiez le fichier variables.txt pour changer l'origine des images :
    - IMAGE_REGISTRY : Registre Docker a utiliser
    - IMAGE_TAG : Tag de l'image a deployer
    - BASE_IMAGE_NAME : Nom de base de l'image
    - BACKEND_IMAGE : Image backend pour deploiement de branche
    - FRONTEND_IMAGE : Image frontend pour deploiement de branche

    Fichiers de configuration :
    - docker-compose.yml : Configuration des services
    - traefik/traefik.yml : Configuration principale Traefik
    - traefik/dynamic.yml : Configuration dynamique Traefik
    - tests.http : Tests de validation des services
    - variables.txt : Variables d'environnement

    Ports pour developpement local (configuration Traefik failover) :
    - andoc : Port 8000 -> /andoc (health: /health)
    - bnqinvt : Port 8001 -> /bnqinvt (health: /health)
    - emp : Port 8004 -> /emp (health: /health)
    - traduc : Port 8005 -> /traduc (health: /health)
    - talperftraitement : Port 8006 -> /talperftraitement (health: /health)
    - ui : Port 4200 -> / racine (health: /)

    AWS Configuration :
    - Profile par defaut : ESG-DV-PowerUser-SSO
    - ECR Registry : 237029655182.dkr.ecr.ca-central-1.amazonaws.com
    - JFrog Registry : cdpq.jfrog.io

.EXAMPLE
    .\launch.ps1
    Demarre tous les services avec la configuration par defaut.

.EXAMPLE
    .\launch.ps1 -c "start"
    Demarre explicitement tous les services.

.EXAMPLE
    .\launch.ps1 -r "andoc" -c "start"
    Demarre uniquement le service "andoc".

.EXAMPLE
    .\launch.ps1 -b "feature/nouvelle-fonctionnalite"
    Demarre les services avec la branche specifiee (tag: feature/nouvelle-fonctionnalite-SNAPSHOT).

.EXAMPLE
    .\launch.ps1 -c "stop"
    Arrete tous les services en cours d'execution.

.EXAMPLE
    .\launch.ps1 -c "sso"
    Se connecte a AWS SSO avec le profil configure.

.EXAMPLE
    .\launch.ps1 -c "dk"
    Effectue la connexion Docker a AWS ECR.

.EXAMPLE
    .\launch.ps1 -h
    Affiche l'aide complete du script avec Get-Help.

.LINK
    README.md

#>
param(
    [string]$r,
    [string]$b,
    [switch]$h,
    [Parameter(Position = 0)]
    [ValidateSet('start', 'stop', 'help', 'sso', 'id', 'dk', 'jfrog')]
    [string]$c='start'
)

# Configuration encodage UTF-8 avec BOM pour PowerShell
$PSDefaultParameterValues['*:Encoding'] = 'UTF8'
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Fonction pour afficher l'aide personnalisée
function Show-Usage {
    Write-Host @"
UTILISATION:
    .\launch.ps1 [-r <service>] [-b <branche>] [-c <commande>] [-h]

PARAMETRES:
    -r <service>    Service specifique a redemarrer
    -b <branche>    Branche Git a utiliser pour le deploiement
    -c <commande>   Commande a executer (start|stop|help|sso|id|dk|jfrog)
    -h             Afficher cette aide

COMMANDES:
    start    Demarrer les services (defaut)
    stop     Arreter les services
    help     Afficher l'aide complete
    sso      Connexion AWS SSO
    id       Afficher l'identite AWS actuelle
    dk       Connexion Docker a AWS ECR
    jfrog    Connexion Docker a JFrog

EXEMPLES:
    .\launch.ps1                    # Demarrer les services
    .\launch.ps1 -c stop           # Arreter les services
    .\launch.ps1 -r andoc          # Redemarrer le service andoc
    .\launch.ps1 -b main           # Deployer la branche main
    .\launch.ps1 -c sso            # Se connecter a AWS SSO

Pour l'aide complete: Get-Help .\launch.ps1 -Full
"@
}

# Helper: Validate required commands
function Validate-Command($cmd) {
    if (-not (Get-Command $cmd -ErrorAction SilentlyContinue)) {
        Write-Error "ERROR: $cmd is not installed or not in PATH."
        exit 1
    }
}

# Helper: Validate Docker Compose version
function Validate-DockerCompose {
    Validate-Command 'docker'
    $ver = docker compose version 2>$null
    if (-not $ver) {
        Write-Error "ERROR: Docker Compose not found or Docker not running."
        exit 1
    }
    $major = ($ver -replace '[^\d\.]', '').Split('.')[0]
    if ([int]$major -lt 2) {
        Write-Error "ERROR: Docker Compose version >= 2 required."
        exit 1
    }
}

# Helper: Validate AWS CLI
function Validate-Aws {
    Validate-Command 'aws'
}

# Helper: Docker Compose Down
function DockerCompose-Down {
    $env:COMPOSE_PROFILES = 'qa,lv,jc,int,mail,mon'
    docker compose -f docker-compose.yml down --remove-orphans
}

# Helper: Docker Compose Pull
function DockerCompose-Pull($profiles) {
    $env:COMPOSE_PROFILES = $profiles
    docker compose -f docker-compose.yml pull
}

# Helper: Docker Compose Up
function DockerCompose-Up($profiles) {
    $env:COMPOSE_PROFILES = $profiles
    docker compose -f docker-compose.yml up -d --wait
}

# Helper: Restart Container
function Restart-Container($profiles, $service) {
    $env:COMPOSE_PROFILES = $profiles
    docker compose -f docker-compose.yml pull $service
    docker compose -f docker-compose.yml up -d $service
}

# Helper: Is Healthy
function Is-Healthy($service) {
    $status = docker inspect -f '{{.State.Health.Status}}' $service 2>$null
    if ($status -eq 'healthy') {
        Write-Host "Service $service is healthy"
        return $true
    } else {
        Write-Host "Service $service is not healthy: $status"
        return $false
    }
}

# Helper: Start Docker
function Start-Docker($profiles) {
    DockerCompose-Pull $profiles
    DockerCompose-Up $profiles
}

# Helper: AWS Login
function Login-Aws {
    Write-Host "Connexion à AWS ECR avec le profil $env:AWS_PROFILE..."
    $session = aws sts get-caller-identity 2>$null
    if (-not $session) {
        aws sso login --profile $env:AWS_PROFILE
    } else {
        Write-Host "Deja connecte!"
    }
}

# Helper: AWS ECR Docker Login
function Login-AwsEcr {
    Write-Host "Connexion Docker à AWS ECR..."
    try {
        # Utiliser cmd.exe pour exécuter la commande exactement comme elle fonctionne manuellement
        $ecrUrl = 'https://237029655182.dkr.ecr.ca-central-1.amazonaws.com'
        cmd /c "aws ecr get-login-password --profile $env:AWS_PROFILE | docker login -u AWS --password-stdin $ecrUrl"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Connexion Docker ECR reussie!"
        } else {
            Write-Error "Echec de la connexion Docker ECR"
            exit 1
        }
    } catch {
        Write-Error "Erreur lors de la connexion Docker ECR: $($_.Exception.Message)"
        exit 1
    }
}

# Helper: JFrog Login
function Login-Jfrog {
    # Cherche une variable d'environnement contenant l'URL JFrog
    $jfrogUrl = 'cdpq.jfrog.io'
    $jfrogImageVar = [Environment]::GetEnvironmentVariables('Process').GetEnumerator() | Where-Object {
        $_.Value -like "*$jfrogUrl*"
    } | Select-Object -First 1

    if (-not $jfrogImageVar) {
        Write-Host 'Aucune image JFrog detectee dans la configuration, docker login ignore.'
        return
    }

    $jfrogLogged = $false
    try {
        $dockerConfig = Get-Content "$env:USERPROFILE\.docker\config.json" -Raw | ConvertFrom-Json
        if ($dockerConfig.auths.'cdpq.jfrog.io') {
            $jfrogLogged = $true
        }
    } catch {}
    if ($jfrogLogged) {
        Write-Host 'Deja connecte a JFrog (docker).'
    } else {
        Write-Host 'Connexion a JFrog requise:'
        docker login cdpq.jfrog.io
    }
}

# Main logic
if ($h -or ($c -eq 'help')) {
    if (Get-Command Get-Help -ErrorAction SilentlyContinue) {
        Get-Help $MyInvocation.MyCommand.Path -Full
    } else {
        Show-Usage
    }
    exit 0
}

$env:AWS_PROFILE='ESG-DV-PowerUser-SSO'

Validate-Command 'docker'
Validate-DockerCompose
Validate-Aws

# Charger les variables du fichier variables.txt
$variablesFile = 'variables.txt'
if (Test-Path $variablesFile) {
    Get-Content $variablesFile | ForEach-Object {
        if ($_ -match '^\s*([A-Z0-9_]+)\s*=\s*(.+?)\s*$') {
            $name = $matches[1]
            $value = $matches[2]
            [System.Environment]::SetEnvironmentVariable($name, $value, 'Process')
        }
    }
}

if ($c -eq 'sso') {
    Write-Host "Connexion AWS SSO avec le profil $env:AWS_PROFILE..."
    aws sso login --profile $env:AWS_PROFILE
    exit 0
}

if ($c -eq 'id') {
    Write-Host 'Identite AWS actuelle:'
    aws sts get-caller-identity --profile $env:AWS_PROFILE
    exit 0
}

if ($c -eq 'dk') {
    Login-AwsEcr
    exit 0
}

if ($c -eq 'jfrog') {
    Write-Host 'Connexion Docker à JFrog:'
    Login-Jfrog
    exit 0
}

if ($c -eq 'stop') {
    DockerCompose-Down
    exit 0
}

if ($r) {
    Login-Aws
    Login-Jfrog
    $profiles = 'qa,lv,jc,int,mail,mon'
    Restart-Container $profiles $r
    exit 0
}

if ($b) {
    $branchSlug = $b.ToLower() + '-SNAPSHOT'
    Login-Aws
    Login-Jfrog
    $backendImage = $env:BACKEND_IMAGE
    $frontendImage = $env:FRONTEND_IMAGE
    if ($backendImage) {
        if (docker image pull "$backendImage`:$branchSlug") {
            $env:BACKEND_VERSION = $branchSlug
        } else {
            Write-Warning "Impossible de trouver l'image [$branchSlug] pour le backend, utilisation de l'image par defaut"
        }
    }
    if ($frontendImage) {
        if (docker image pull "$frontendImage`:$branchSlug") {
            $env:FRONTEND_VERSION = $branchSlug
        } else {
            Write-Warning "Impossible de trouver l'image [$branchSlug] pour le frontend, utilisation de l'image par defaut"
        }
    }
}

if ($c -eq 'start' -or !$c) {
    Login-Aws
    Login-Jfrog
    $profiles = 'qa,lv,jc,int,mail,mon'
    Start-Docker $profiles
    exit 0
}

Show-Usage
exit 1

# Pour verifier le statut de la session SSO AWS :
# aws sso get-role-credentials --profile <votre_profile> --role-name <role> --account-id <account_id>
# ou plus simplement pour verifier l'identite actuelle (fonctionne si la session SSO est valide) :
# aws sts get-caller-identity --profile <votre_profile>
# aws sts get-caller-identity --profile ESG-TA-PowerUser-SSO
